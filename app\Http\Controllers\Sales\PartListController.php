<?php

namespace App\Http\Controllers\Sales;

use App\Http\Controllers\Controller;
use App\Models\Part;
use App\Models\PartInventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PartListController extends Controller
{
    /**
     * Display the part list page
     */
    public function index()
    {
        return view('sales.part-list');
    }

    /**
     * Get parts data for AJAX request
     */
    public function getData(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $perPage = 10;
            $search = $request->get('search', '');

            // Query only the parts table directly - no joins or relationships
            $query = Part::select([
                'part_code',
                'part_name',
                'part_type',
                'price',
                'purchase_price',
                'eum',
                'bin_location',
                'created_at',
                'updated_at'
            ])->orderBy('part_name', 'asc');

            // Apply search filter
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('part_name', 'LIKE', "%{$search}%")
                      ->orWhere('part_code', 'LIKE', "%{$search}%")
                      ->orWhere('part_type', 'LIKE', "%{$search}%");
                });
            }

            // Get paginated results - each part will appear only once
            $parts = $query->paginate($perPage, ['*'], 'page', $page);

            // Return data directly without additional formatting
            return response()->json([
                'success' => true,
                'data' => $parts->items(),
                'pagination' => [
                    'current_page' => $parts->currentPage(),
                    'last_page' => $parts->lastPage(),
                    'per_page' => $parts->perPage(),
                    'total' => $parts->total(),
                    'from' => $parts->firstItem(),
                    'to' => $parts->lastItem()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching parts data: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data part'
            ], 500);
        }
    }

    /**
     * Store a new part
     */
    public function store(Request $request)
    {
        try {
            // Validation rules
            $validator = Validator::make($request->all(), [
                'part_code' => 'required|string|max:50|unique:parts,part_code',
                'part_name' => 'required|string|max:255',
                'part_type' => 'required|in:AC,TYRE,FABRIKASI,PERLENGKAPAN AC,PERSEDIAAN LAINNYA',
                'price' => 'nullable|numeric|min:0',
                'purchase_price' => 'nullable|numeric|min:0',
                'eum' => 'nullable|string|max:5'
            ], [
                'part_code.required' => 'Kode part harus diisi',
                'part_code.unique' => 'Kode part sudah digunakan',
                'part_code.max' => 'Kode part maksimal 50 karakter',
                'part_name.required' => 'Nama part harus diisi',
                'part_name.max' => 'Nama part maksimal 255 karakter',
                'part_type.required' => 'Tipe part harus dipilih',
                'part_type.in' => 'Tipe part tidak valid',
                'price.numeric' => 'Harga harus berupa angka',
                'price.min' => 'Harga tidak boleh negatif',
                'purchase_price.numeric' => 'Harga beli harus berupa angka',
                'purchase_price.min' => 'Harga beli tidak boleh negatif',
                'eum.max' => 'EUM maksimal 5 karakter'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Create new part
            $part = Part::create([
                'part_code' => $request->part_code,
                'part_name' => $request->part_name,
                'part_type' => $request->part_type,
                'price' => $request->price,
                'purchase_price' => $request->purchase_price,
                'eum' => $request->eum ?: 'EA',
                'bin_location' => '-' // Default bin location
            ]);

            Log::info("New part created: {$part->part_code} - {$part->part_name}");

            return response()->json([
                'success' => true,
                'message' => 'Part berhasil ditambahkan',
                'data' => $part
            ]);

        } catch (\Exception $e) {
            Log::error('Error creating part: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menambahkan part'
            ], 500);
        }
    }

    /**
     * Update part data
     */
    public function update(Request $request, $partCode)
    {
        try {
            // Validation rules
            $validator = Validator::make($request->all(), [
                'part_name' => 'required|string|max:255',
                'part_type' => 'required|in:AC,TYRE,FABRIKASI,PERLENGKAPAN AC,PERSEDIAAN LAINNYA',
                'price' => 'nullable|numeric|min:0',
                'purchase_price' => 'nullable|numeric|min:0',
                'eum' => 'nullable|string|max:5'
            ], [
                'part_name.required' => 'Nama part harus diisi',
                'part_name.max' => 'Nama part maksimal 255 karakter',
                'part_type.required' => 'Tipe part harus dipilih',
                'part_type.in' => 'Tipe part tidak valid',
                'price.numeric' => 'Harga harus berupa angka',
                'price.min' => 'Harga tidak boleh negatif',
                'purchase_price.numeric' => 'Harga beli harus berupa angka',
                'purchase_price.min' => 'Harga beli tidak boleh negatif',
                'eum.max' => 'EUM maksimal 5 karakter'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Find the part
            $part = Part::where('part_code', $partCode)->first();
            if (!$part) {
                return response()->json([
                    'success' => false,
                    'message' => 'Part tidak ditemukan'
                ], 404);
            }

            // Store old part name for comparison
            $oldPartName = $part->part_name;
            $newPartName = $request->part_name;

            // Update part data
            $part->update([
                'part_name' => $newPartName,
                'part_type' => $request->part_type,
                'price' => $request->price,
                'purchase_price' => $request->purchase_price,
                'eum' => $request->eum
            ]);

            // Critical Business Rule: Update site inventories if part name changed
            if ($oldPartName !== $newPartName) {
                PartInventory::where('part_code', $partCode)
                    ->whereNull('site_part_name') // Only update if no custom site name
                    ->update(['site_part_name' => $newPartName]);

                Log::info("Part name updated from '{$oldPartName}' to '{$newPartName}' for part {$partCode}. Site inventories synchronized.");
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data part berhasil diperbarui',
                'data' => $part->fresh()
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating part: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui data part'
            ], 500);
        }
    }

    /**
     * Get part types for dropdown
     */
    public function getPartTypes()
    {
        $partTypes = ['AC', 'TYRE', 'FABRIKASI', 'PERLENGKAPAN AC', 'PERSEDIAAN LAINNYA'];

        return response()->json([
            'success' => true,
            'data' => $partTypes
        ]);
    }
}
