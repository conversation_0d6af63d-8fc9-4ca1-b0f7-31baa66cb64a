import Swal from "sweetalert2";

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 10; // 10 items per page
let selectedUnitId = null;
let partsCurrentPage = 1;
let unitParts = []; // Array to store parts for the current unit
let temporaryParts = []; // Array to store parts to be added to a unit

// Function to create pagination item
function createPaginationItem(page, text, isActive = false) {
    const li = document.createElement('li');
    li.className = `page-item ${isActive ? 'active' : ''}`;

    const a = document.createElement('a');
    a.className = 'page-link';
    a.href = '#';
    a.textContent = text;
    a.dataset.page = page;

    li.appendChild(a);
    return li;
}

// Function to render pagination
function renderPagination(data, containerId, loadFunction) {
    try {
        const paginationContainer = document.getElementById(containerId);
        if (!paginationContainer) return;

        paginationContainer.innerHTML = '';

        if (data.total <= data.per_page) return;

        const pagination = document.createElement('ul');
        pagination.className = 'pagination pagination-rounded  pagination-rounded justify-content-center mb-0';

        // Previous button
        if (data.current_page > 1) {
            pagination.appendChild(createPaginationItem(data.current_page - 1, '«'));
        }

        // Page numbers
        const totalPages = data.last_page;
        const currentPage = data.current_page;

        // Calculate range of pages to show
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);

        // Adjust start if we're near the end
        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }

        // First page
        if (startPage > 1) {
            pagination.appendChild(createPaginationItem(1, '1'));
            if (startPage > 2) {
                const ellipsis = createPaginationItem(startPage - 1, '...');
                ellipsis.classList.add('disabled');
                pagination.appendChild(ellipsis);
            }
        }

        // Page numbers
        for (let i = startPage; i <= endPage; i++) {
            pagination.appendChild(createPaginationItem(i, i.toString(), i === currentPage));
        }

        // Last page
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsis = createPaginationItem(endPage + 1, '...');
                ellipsis.classList.add('disabled');
                pagination.appendChild(ellipsis);
            }
            pagination.appendChild(createPaginationItem(totalPages, totalPages.toString()));
        }

        // Next button
        if (data.current_page < totalPages) {
            pagination.appendChild(createPaginationItem(data.current_page + 1, '»'));
        }

        paginationContainer.appendChild(pagination);

        // Add event listeners to pagination links
        paginationContainer.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = parseInt(this.dataset.page);
                loadFunction(page);
            });
        });
    } catch (error) {
        console.error('Error rendering pagination:', error);
    }
}

// Function to load units with pagination
async function loadUnits(page = 1) {
    currentPage = page;
    const searchTerm = document.getElementById('unit-search')?.value || '';

    // Show loading indicator
    const tbody = document.getElementById('unitsTable');
    tbody.innerHTML = '<tr><td colspan="4" class="text-center">Memuat...</td></tr>';

    try {
        // Build query parameters
        const params = new URLSearchParams({
            page: page,
            per_page: itemsPerPage
        });

        // Add search parameter if provided
        if (searchTerm) {
            params.append('search', searchTerm);
        }

        const response = await fetch(`/units-list?${params}`);

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Server returned non-JSON response. Please check server logs.');
        }

        const data = await response.json();

        // Update the search input with the current search term
        // This ensures the search term is preserved when navigating between pages
        const searchInput = document.getElementById('unit-search');
        if (searchInput && searchTerm) {
            searchInput.value = searchTerm;
        }

        if (data.data && data.data.length > 0) {
            tbody.innerHTML = data.data.map(unit => `
                <tr class="border-t">
                    <td class="px-4 py-2">${unit.unit_code}</td>
                    <td class="px-4 py-2">${unit.unit_type}</td>
                    <td class="px-4 py-2">${unit.site.site_name}</td>
                    <td class="px-4 py-2">
                        <button onclick="editUnit(${unit.id})" class="btn-sm btn btn-primary">Edit</button>
                        <button onclick="deleteUnit(${unit.id})" class="btn-sm btn btn-danger ml-2">Delete</button>
                    </td>
                </tr>
            `).join('');
        } else {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center">Tidak ada unit yang ditemukan</td></tr>';
        }

        // Render pagination
        renderPagination({
            current_page: data.current_page || 1,
            per_page: data.per_page || itemsPerPage,
            last_page: data.last_page || 1,
            total: data.total || 0
        }, 'unitsPagination', loadUnits);
    } catch (error) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">Gagal memuat unit</td></tr>';
    }
}

// Function to load parts for a unit with pagination
async function loadParts(unitId, page = 1) {
    partsCurrentPage = page;
    selectedUnitId = unitId;

    // Show loading indicator
    const tbody = document.getElementById('partsTable');
    if (!tbody) return;

    tbody.innerHTML = '<tr><td colspan="5" class="text-center">Memuat...</td></tr>';

    try {
        // Build query parameters
        const params = new URLSearchParams({
            page: page,
            per_page: itemsPerPage
        });

        const response = await fetch(`/units/${unitId}/parts?${params}`);

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Server returned non-JSON response. Please check server logs.');
        }

        const data = await response.json();
        unitParts = data.data || [];

        if (data.data && data.data.length > 0) {
            tbody.innerHTML = data.data.map(part => `
                <tr class="border-t" data-part-inventory-id="${part.part_inventory_id}">
                    <td class="px-4 py-2">${part.part_inventory.part.part_name} (${part.part_inventory.part.part_code})</td>
                    <td class="px-4 py-2">${part.quantity}</td>
                    <td class="px-4 py-2">${formatRupiah(part.price)}</td>
                    <td class="px-4 py-2">${part.eum}</td>
                    <td class="px-4 py-2">
                        <button type="button" class="edit-part-btn btn-sm text-blue-500" data-part-id="${part.id}">Edit</button>
                        <button type="button" class="delete-part-btn btn-sm text-red-500 ml-2" data-part-id="${part.id}">Delete</button>
                    </td>
                </tr>
            `).join('');

            // Add event listeners to edit and delete buttons
            tbody.querySelectorAll('.edit-part-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const partId = this.getAttribute('data-part-id');
                    editPart(partId);
                });
            });

            tbody.querySelectorAll('.delete-part-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const partId = this.getAttribute('data-part-id');
                    deletePart(partId);
                });
            });
        } else {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center">Tidak ada part yang ditemukan untuk unit ini</td></tr>';
        }

        // Render pagination
        renderPagination({
            current_page: data.current_page || 1,
            per_page: data.per_page || itemsPerPage,
            last_page: data.last_page || 1,
            total: data.total || 0
        }, 'partsPagination', (page) => loadParts(unitId, page));
    } catch (error) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center">Gagal memuat part</td></tr>';
    }
}

// Function to edit a unit
async function editUnit(unitId) {
    try {
        const response = await fetch(`/units/${unitId}`);
        const unit = await response.json();

        document.getElementById('unitId').value = unit.id;
        document.getElementById('site_id').value = unit.site_id;
        document.getElementById('unit_code').value = unit.unit_code;
        document.getElementById('unit_type').value = unit.unit_type;

        // Update the parts list title with unit information
        const partsListTitle = document.getElementById('partsListTitle');
        if (partsListTitle) {
            partsListTitle.textContent = `LIST Parts dalam units ${unit.unit_code} - ${unit.unit_type}`;
        }

        // Show the existing parts container
        const existingPartsContainer = document.getElementById('existingPartsContainer');
        if (existingPartsContainer) {
            existingPartsContainer.classList.remove('hidden');
        }

        // Clear temporary parts when editing
        temporaryParts = [];
        updateTemporaryPartsTable();

        // Load parts for this unit
        selectedUnitId = unitId;
        loadParts(unitId, 1);
    } catch (error) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Gagal memuat detail unit'
        });
    }
}

// Function to delete a unit
function deleteUnit(unitId) {
    Swal.fire({
        title: 'Yakin ingin menghapus?',
        text: "Data yang dihapus tidak dapat dikembalikan!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then(async (result) => {
        if (result.isConfirmed) {
            try {
                const response = await fetch(`/units/${unitId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    Swal.fire(
                        'Terhapus',
                        result.message,
                        'success'
                    );
                    loadUnits(currentPage);
                } else {
                    throw new Error(result.message || 'Gagal menghapus unit');
                }
            } catch (error) {
                Swal.fire(
                    'Error',
                    'Maaf, Unit memiliki part terkait',
                    'error'
                );
            }
        }
    });
}

// Function to edit a part
async function editPart(partId) {
    try {
        console.log('Editing part with ID:', partId);

        // Show loading indicator
        Swal.fire({
            title: 'Loading...',
            text: 'Memuat detail part',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        const response = await fetch(`/units/parts/${partId}`);

        // Check if response is OK
        if (!response.ok) {
            throw new Error(`Server returned status ${response.status}: ${response.statusText}`);
        }

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Server returned non-JSON response. Please check server logs.');
        }

        const part = await response.json();
        console.log('Part data received:', part);

        // Close loading indicator
        Swal.close();

        // Check if part data is valid
        if (!part || !part.part_inventory || !part.part_inventory.part) {
            throw new Error('Invalid part data received from server');
        }

        // Open a modal to edit the part
        Swal.fire({
            title: 'Edit Part',
            html: `
                <div class="text-left">
                    <p class="mb-2 font-bold">${part.part_inventory.part.part_name} (${part.part_inventory.part.part_code})</p>

                    <div class="mb-3">
                        <label for="edit-quantity" class="block text-sm font-medium text-gray-700">Jumlah</label>
                        <input type="number" id="edit-quantity" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" value="${part.quantity}" min="0.1" step="0.1">
                    </div>

                    <div class="mb-3">
                        <label for="edit-price" class="block text-sm font-medium text-gray-700">Harga (Rp)</label>
                        <input type="text" id="edit-price" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" value="${formatRupiah(part.price)}">
                        <input type="hidden" id="edit-price-raw" value="${part.price}">
                    </div>

                    <div class="mb-3">
                        <label for="edit-eum" class="block text-sm font-medium text-gray-700">EUM</label>
                        <input type="text" id="edit-eum" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" value="${part.eum}">
                    </div>
                </div>
            `,
            didOpen: () => {
                // Add event listener to format price as Rupiah when typing
                const priceInput = document.getElementById('edit-price');
                const priceRawInput = document.getElementById('edit-price-raw');

                priceInput.addEventListener('input', function() {
                    // Simpan posisi kursor
                    const cursorPos = this.selectionStart;
                    const oldLength = this.value.length;

                    // Parse nilai Rupiah saat ini (untuk debugging)
                    parseRupiah(this.value); // Memanggil fungsi untuk memastikan digunakan

                    // Hapus semua karakter non-angka kecuali untuk input kosong
                    let value = this.value.replace(/[^\d]/g, '');

                    // Konversi ke angka (atau 0 jika kosong)
                    const numValue = value === '' ? 0 : parseInt(value);

                    // Update the hidden raw price input
                    priceRawInput.value = numValue;

                    // Format the visible input as Rupiah
                    const formattedValue = formatRupiah(numValue);
                    this.value = formattedValue;

                    // Hitung perubahan panjang untuk menyesuaikan posisi kursor
                    const newLength = this.value.length;
                    const lengthDiff = newLength - oldLength;

                    // Setel posisi kursor yang disesuaikan
                    // Jika kursor di akhir, biarkan di akhir
                    if (cursorPos === oldLength) {
                        this.setSelectionRange(newLength, newLength);
                    } else {
                        // Jika tidak, sesuaikan posisi kursor
                        const newPos = Math.max(0, Math.min(cursorPos + lengthDiff, newLength));
                        this.setSelectionRange(newPos, newPos);
                    }
                });
            },
            showCancelButton: true,
            confirmButtonText: 'Simpan Perubahan',
            cancelButtonText: 'Batal',
            showLoaderOnConfirm: true,
            preConfirm: async () => {
                try {
                    const quantity = document.getElementById('edit-quantity').value;
                    const price = document.getElementById('edit-price-raw').value;
                    const eum = document.getElementById('edit-eum').value;

                    // Validate inputs
                    if (!quantity || parseFloat(quantity) <= 0) {
                        Swal.showValidationMessage('Jumlah harus lebih besar dari 0');
                        return false;
                    }

                    if (!price || price < 0) {
                        Swal.showValidationMessage('Harga harus berupa angka yang valid');
                        return false;
                    }

                    // Send update request
                    const response = await fetch(`/units/parts/${partId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            quantity: quantity,
                            price: price,
                            eum: eum
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`Server returned status ${response.status}: ${response.statusText}`);
                    }

                    return await response.json();
                } catch (error) {
                    Swal.showValidationMessage(`Error: ${error.message}`);
                    return false;
                }
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.isConfirmed && result.value) {
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil',
                    text: 'Part berhasil diperbarui'
                });

                // Reload parts list
                loadParts(selectedUnitId, partsCurrentPage);
            }
        });

    } catch (error) {
        console.error('Error editing part:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: `Gagal memuat detail part: ${error.message}`
        });
    }
}

// Function to delete a part
function deletePart(partId) {
    Swal.fire({
        title: 'Yakin ingin menghapus?',
        text: "Data yang dihapus tidak dapat dikembalikan!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
    }).then(async (result) => {
        if (result.isConfirmed) {
            try {
                const response = await fetch(`/units/parts/${partId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    Swal.fire(
                        'Terhapus',
                        result.message,
                        'success'
                    );
                    loadParts(selectedUnitId, partsCurrentPage);
                } else {
                    throw new Error(result.message || 'Gagal menghapus part');
                }
            } catch (error) {
                Swal.fire(
                    'Error',
                    'Gagal menghapus part',
                    'error'
                );
            }
        }
    });
}

// Function to add a part to the temporary list
function addPartToTemporaryList() {
    const partInventoryId = document.getElementById('part_inventory_id').value;
    const partName = document.getElementById('part_search').value;
    const quantity = document.getElementById('quantity').value;
    const price = document.getElementById('price').value;
    const eum = document.getElementById('eum').value;

    // Validate inputs
    if (!partInventoryId || !partName) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Silakan pilih part terlebih dahulu'
        });
        return;
    }

    if (!quantity || parseFloat(quantity) <= 0) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Jumlah harus lebih besar dari 0'
        });
        return;
    }

    if (!price || price < 0) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Harga harus berupa angka yang valid'
        });
        return;
    }

    // Check if part already exists in the temporary list
    const existingPartIndex = temporaryParts.findIndex(part => part.part_inventory_id === partInventoryId);
    if (existingPartIndex !== -1) {
        Swal.fire({
            icon: 'warning',
            title: 'Part Sudah Ditambahkan',
            text: 'Part ini sudah ada dalam daftar. Anda dapat menghapusnya dan menambahkannya kembali dengan jumlah yang berbeda jika diperlukan.'
        });
        return;
    }

    // Check if part already exists in the unit (for existing units)
    if (isPartAlreadyAdded(partInventoryId)) {
        Swal.fire({
            icon: 'warning',
            title: 'Part Sudah Ada',
            text: 'Part ini sudah ada dalam unit. Anda dapat mengedit atau menghapus part yang ada terlebih dahulu.'
        });
        return;
    }

    // Add part to temporary list
    temporaryParts.push({
        part_inventory_id: partInventoryId,
        part_name: partName,
        quantity: quantity,
        price: price,
        eum: eum
    });

    // Update the temporary parts table
    updateTemporaryPartsTable();

    // Reset part form fields
    document.getElementById('part_inventory_id').value = '';
    document.getElementById('part_search').value = '';
    document.getElementById('price').value = '0';
    document.getElementById('price_display').value = formatRupiah(0);
    document.getElementById('quantity').value = '1';
    document.getElementById('eum').value = 'EA';
}

// Function to remove a part from the temporary list
function removePartFromTemporaryList(index) {
    temporaryParts.splice(index, 1);
    updateTemporaryPartsTable();
}

// Function to update the temporary parts table
function updateTemporaryPartsTable() {
    const tableBody = document.getElementById('temporaryPartsTableBody');
    const newPartsContainer = document.getElementById('newPartsContainer');
    const emptyMessage = document.getElementById('emptyPartsMessage');

    // Check if we have any parts (either existing or temporary)
    const hasExistingParts = selectedUnitId && document.getElementById('partsTable').querySelectorAll('tr').length > 0;

    if (temporaryParts.length === 0 && !hasExistingParts) {
        // No parts at all
        if (newPartsContainer) newPartsContainer.classList.add('hidden');
        if (emptyMessage) emptyMessage.classList.remove('hidden');
        return;
    }

    // We have some parts (either existing or temporary)
    if (emptyMessage) emptyMessage.classList.add('hidden');

    // Handle temporary parts
    if (temporaryParts.length === 0) {
        // No temporary parts
        if (newPartsContainer) newPartsContainer.classList.add('hidden');
    } else {
        // We have temporary parts
        if (newPartsContainer) newPartsContainer.classList.remove('hidden');

        tableBody.innerHTML = '';

        temporaryParts.forEach((part, index) => {
            const row = document.createElement('tr');
            row.className = 'border-t';

            row.innerHTML = `
                <td class="px-4 py-2">${part.part_name}</td>
                <td class="px-4 py-2">${part.quantity}</td>
                <td class="px-4 py-2">${formatRupiah(part.price)}</td>
                <td class="px-4 py-2">${part.eum}</td>
                <td class="px-4 py-2">
                    <button type="button" class="text-red-500 remove-part" data-index="${index}">Hapus</button>
                </td>
            `;

            tableBody.appendChild(row);
        });

        // Add event listeners to remove buttons
        document.querySelectorAll('.remove-part').forEach(button => {
            button.addEventListener('click', function() {
                const index = parseInt(this.dataset.index);
                removePartFromTemporaryList(index);
            });
        });
    }
}

// Function to reset the combined form
function resetCombinedForm() {
    document.getElementById('combinedUnitForm').reset();
    document.getElementById('unitId').value = '';
    document.getElementById('part_inventory_id').value = '';
    document.getElementById('part_search').value = '';
    document.getElementById('price').value = '0';
    document.getElementById('eum').value = 'EA';
    document.getElementById('price_display').value = formatRupiah(0);
    document.getElementById('quantity').value = '1';

    // Reset the parts list title
    const partsListTitle = document.getElementById('partsListTitle');
    if (partsListTitle) {
        partsListTitle.textContent = 'LIST Parts';
    }

    // Hide the existing parts container
    const existingPartsContainer = document.getElementById('existingPartsContainer');
    if (existingPartsContainer) {
        existingPartsContainer.classList.add('hidden');
    }

    // Hide the new parts container
    const newPartsContainer = document.getElementById('newPartsContainer');
    if (newPartsContainer) {
        newPartsContainer.classList.add('hidden');
    }

    // Show the empty message
    const emptyMessage = document.getElementById('emptyPartsMessage');
    if (emptyMessage) {
        emptyMessage.classList.remove('hidden');
    }

    // Reset selected unit ID
    selectedUnitId = null;

    // Clear temporary parts list
    temporaryParts = [];
    updateTemporaryPartsTable();
}

// Format number to Indonesian Rupiah
function formatRupiah(number) {
    // Pastikan number adalah angka
    if (isNaN(number) || number === null) number = 0;

    // Format angka ke format Rupiah Indonesia
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(number);
}

// Parse Rupiah string to number
function parseRupiah(rupiahString) {
    if (!rupiahString) return 0;

    // Hapus semua karakter non-digit
    const cleanString = rupiahString.replace(/[^\d]/g, '');

    // Konversi ke integer atau 0 jika string kosong
    return cleanString === '' ? 0 : parseInt(cleanString);
}

// Initialize part inventory autocomplete
function initPartInventoryAutocomplete() {
    const partSearchInput = document.getElementById('part_search');
    const partInventoryInput = document.getElementById('part_inventory_id');
    const partSuggestions = document.getElementById('part_suggestions');
    const quantityInput = document.getElementById('quantity');
    const priceInput = document.getElementById('price');
    const priceDisplay = document.getElementById('price_display');
    const eumInput = document.getElementById('eum');

    // Set default quantity to 1
    if (quantityInput && quantityInput.value === '') {
        quantityInput.value = 1;
    }

    if (!partSearchInput || !partInventoryInput || !partSuggestions) return;

    partSearchInput.addEventListener('input', debounce(async function() {
        const query = this.value.trim();
        if (query.length < 2) {
            partSuggestions.innerHTML = '';
            partSuggestions.style.display = 'none';
            return;
        }

        try {
            const response = await fetch(`/part-inventories/search?query=${query}`);
            const data = await response.json();

            if (data.length > 0) {
                partSuggestions.innerHTML = data.map(item => `
                    <div class="p-2 border-b cursor-pointer hover:bg-gray-100"
                         data-id="${item.part_inventory_id}"
                         data-name="${item.part.part_name}"
                         data-code="${item.part.part_code}"
                         data-price="${item.price || 0}">
                        ${item.part.part_name} (${item.part.part_code})
                    </div>
                `).join('');

                partSuggestions.style.display = 'block';

                // Add click event to suggestions
                partSuggestions.querySelectorAll('div').forEach(div => {
                    div.addEventListener('click', function() {
                        const partId = this.dataset.id;
                        const partName = this.dataset.name;
                        const partCode = this.dataset.code;
                        const partPrice = this.dataset.price || 0;

                        // Check if this part is already added to the unit
                        if (isPartAlreadyAdded(partId)) {
                            Swal.fire({
                                icon: 'warning',
                                title: 'Part Duplikat',
                                text: 'Part ini sudah ditambahkan ke unit.'
                            });
                            return;
                        }

                        // Set the part inventory ID and name
                        partInventoryInput.value = partId;
                        partSearchInput.value = `${partName} (${partCode})`;

                        // Set default quantity to 1 if not already set
                        if (quantityInput && (!quantityInput.value || quantityInput.value === '')) {
                            quantityInput.value = 1;
                        }

                        // Set price from part inventory (default to 0 if not available)
                        if (priceInput && priceDisplay) {
                            priceInput.value = partPrice;
                            priceDisplay.value = formatRupiah(partPrice);
                        }

                        // Set default EUM to 'EA' if not already set
                        if (eumInput && (!eumInput.value || eumInput.value === '')) {
                            eumInput.value = 'EA';
                        }

                        partSuggestions.style.display = 'none';
                    });
                });
            } else {
                partSuggestions.innerHTML = '<div class="p-2">Tidak ada part yang ditemukan</div>';
                partSuggestions.style.display = 'block';
            }
        } catch (error) {
            console.error('Error searching parts:', error);
        }
    }, 300));

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!partSearchInput.contains(e.target) && !partSuggestions.contains(e.target)) {
            partSuggestions.style.display = 'none';
        }
    });

    // Initialize price formatter
    if (priceDisplay && priceInput) {
        priceDisplay.addEventListener('input', function() {
            // Simpan posisi kursor
            const cursorPos = this.selectionStart;
            const oldLength = this.value.length;

            // Hapus semua karakter non-angka
            let value = this.value.replace(/[^\d]/g, '');

            // Konversi ke angka (atau 0 jika kosong)
            const numValue = value === '' ? 0 : parseInt(value);

            // Update hidden price input
            priceInput.value = numValue;

            // Format the visible input as Rupiah
            const formattedValue = formatRupiah(numValue);
            this.value = formattedValue;

            // Hitung perubahan panjang untuk menyesuaikan posisi kursor
            const newLength = this.value.length;
            const lengthDiff = newLength - oldLength;

            // Setel posisi kursor yang disesuaikan
            // Jika kursor di akhir, biarkan di akhir
            if (cursorPos === oldLength) {
                this.setSelectionRange(newLength, newLength);
            } else {
                // Jika tidak, sesuaikan posisi kursor
                const newPos = Math.max(0, Math.min(cursorPos + lengthDiff, newLength));
                this.setSelectionRange(newPos, newPos);
            }
        });
    }
}

// Check if a part is already added to the unit
function isPartAlreadyAdded(partInventoryId) {
    // Check in the parts table
    const partsTable = document.getElementById('partsTable');
    if (partsTable) {
        const partRows = Array.from(partsTable.querySelectorAll('tr[data-part-inventory-id]'));
        if (partRows.some(row => row.dataset.partInventoryId === partInventoryId)) {
            return true;
        }
    }

    // Check in the temporary parts list
    if (temporaryParts.some(part => part.part_inventory_id.toString() === partInventoryId.toString())) {
        return true;
    }

    // Also check in the unitParts array
    return unitParts.some(part => part.part_inventory_id.toString() === partInventoryId.toString());
}

// Debounce function to limit API calls
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

// Document ready function
document.addEventListener('DOMContentLoaded', function() {
    // Set default quantity to 1 when the page loads
    const quantityInput = document.getElementById('quantity');
    if (quantityInput) {
        quantityInput.value = 1;
    }

    // Add event listener for unit search
    const unitSearchInput = document.getElementById('unit-search');
    if (unitSearchInput) {
        unitSearchInput.addEventListener('input', debounce(function() {
            // Always reset to page 1 when searching
            loadUnits(1);
        }, 300));
    }

    // Initialize Add Part button
    const addPartButton = document.getElementById('addPartToList');
    if (addPartButton) {
        addPartButton.addEventListener('click', addPartToTemporaryList);
    }

    // Initialize combined form submission
    const combinedUnitForm = document.getElementById('combinedUnitForm');
    if (combinedUnitForm) {
        combinedUnitForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Get unit data
            const unitId = document.getElementById('unitId').value;
            const unitCode = document.getElementById('unit_code').value;
            const unitType = document.getElementById('unit_type').value;
            const siteId = document.getElementById('site_id').value;

            // Validate required fields
            if (!unitCode || !unitType) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Unit Code dan Unit Type harus diisi'
                });
                return;
            }

            // Check if there are parts in the temporary list when creating a new unit
            if (!unitId && temporaryParts.length === 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Tambahkan minimal satu part ke unit'
                });
                return;
            }

            // When editing, if no new parts are added, just update the unit details
            if (unitId && temporaryParts.length === 0) {
                // Show confirmation
                const confirmResult = await Swal.fire({
                    icon: 'question',
                    title: 'Perbarui Unit Saja?',
                    text: 'Tidak ada part baru yang ditambahkan. Apakah Anda ingin memperbarui detail unit saja?',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, perbarui unit saja',
                    cancelButtonText: 'Batal'
                });

                if (!confirmResult.isConfirmed) {
                    return;
                }
            }

            // Prepare data for submission
            const unitData = {
                id: unitId || null,
                unit_code: unitCode,
                unit_type: unitType,
                site_id: siteId
            };

            // Prepare parts data
            const partsData = temporaryParts.map(part => ({
                part_inventory_id: part.part_inventory_id,
                quantity: part.quantity,
                price: part.price,
                eum: part.eum
            }));

            const submitData = {
                unit: unitData,
                parts: partsData
            };

            try {
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (!csrfToken) {
                    throw new Error('CSRF token tidak ditemukan. Silakan refresh halaman.');
                }

                // Determine URL and method based on whether we're creating or updating
                const url = unitId ? `/units/${unitId}/with-part` : '/units/with-part';
                const method = unitId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                    },
                    body: JSON.stringify(submitData)
                });

                // Log response for debugging
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                // Try to parse JSON response
                let result;
                try {
                    result = await response.json();
                    console.log('Response body:', result);
                } catch (jsonError) {
                    console.error('Error parsing JSON:', jsonError);
                    throw new Error('Server returned invalid JSON response');
                }

                if (response.ok) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: result.message || 'Unit dan Part berhasil disimpan'
                    });
                    resetCombinedForm();
                    loadUnits(currentPage);
                } else {
                    throw new Error(result.message || `Error ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: error.message || 'Gagal menyimpan unit dan part'
                });
            }
        });
    }

    // Initialize part inventory autocomplete
    initPartInventoryAutocomplete();

    // Initial load of units
    loadUnits(1);

    // Expose functions to window object for inline event handlers
    window.loadUnits = loadUnits;
    window.loadParts = loadParts;
    window.editUnit = editUnit;
    window.deleteUnit = deleteUnit;
    window.editPart = editPart;
    window.deletePart = deletePart;
    window.resetCombinedForm = resetCombinedForm;
});
