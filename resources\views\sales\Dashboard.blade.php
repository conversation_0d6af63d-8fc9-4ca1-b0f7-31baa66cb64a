@extends('sales.contentsales')
@section('resourcesales')
@vite(['resources/js/sales/dashboard.js'])
<style>
    .w-fit-content {
        width: fit-content;
    }
    .btn{
        font-size: 11px;
    }
    .btn-warning{
        background-color:rgb(242, 215, 132);
        color: #000;
    }
    .bg-warning{
        background-color:rgb(240, 250, 150);
        color: #0f0187;
    }

    /* Sortable table styles */
    .sortable {
        cursor: pointer;
        position: relative;
    }

    .sortable:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .sort-icon {
        font-size: 11px;
        margin-left: 5px;
        opacity: 0.5;
    }

    .sortable.asc .sort-icon {
        opacity: 1;
        transform: rotate(180deg);
    }

    .sortable.desc .sort-icon {
        opacity: 1;
        transform: rotate(0deg);
    }
</style>
@endsection
@section('contentsales')

<div class="bgwhite mb-2 p-2 pr-2">
    <div class="d-flex justify-content-right">
        <div class="nav-links">
            <a href="{{ route('sales.dashboard') }}" class="btn btn-sm {{ request()->routeIs('sales.dashboard') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-view-dashboard"></i> Dashboard
            </a>
            <a href="{{ route('sales.penawaran') }}" class="btn btn-sm {{ request()->routeIs('sales.penawaran') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-file-document-edit"></i> Buat Penawaran
            </a>
            <a href="{{ route('sales.invoices') }}" class="btn btn-sm {{ request()->routeIs('sales.invoices') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-file-check"></i> Invoices
            </a>
            <a href="{{ route('sales.part-list') }}" class="btn btn-sm {{ request()->routeIs('sales.part-list') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-format-list-bulleted"></i> Part List
            </a>
            <a href="{{ route('sales.jasa-karyawan') }}" class="btn btn-sm {{ request()->routeIs('sales.jasa-karyawan') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-account-cash"></i>Monthly Report
            </a>
            <a class="btn btn-sm btn-secondary mx-1" href="{{ route(name: 'logout') }}">
                <i class="mdi mdi-logout-variant"></i>
                <span> Logout</span>
            </a>
        </div>
    </div>
</div>
<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="bgwhite shadow-kit rounded-lg ">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <button id="generate-invoice-btn" class="btn btn-sm btn-success" disabled><i class="mdi mdi-file-document"></i>Buat Invoice</button>
                            <h5 class="ml-4 mb-0 mr-3 font-bold text-uppercase text-white">Unit Transactions</h5>
                        </div>
                        <div class="d-flex">
                            <input type="date" id="date-from" class="form-control form-control-sm mr-2" value="{{ now()->startOfMonth()->format('Y-m-d') }}">
                            <input type="date" id="date-to" class="form-control form-control-sm mr-2" value="{{ now()->format('Y-m-d') }}">
                            <select id="site-filter" class="form-control form-control-sm mr-2">
                                <option value="">Semua Site</option>
                                @foreach($sites as $site)
                                <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                                @endforeach
                            </select>
                            <select id="unit-filter" class="form-control form-control-sm mr-2">
                                <option value="">Semua Unit</option>
                                <!-- Units will be loaded dynamically based on selected site -->
                            </select>
                            <select id="status-filter" class="form-control form-control-sm mr-2">
                                <option value="">Semua Status</option>
                                @foreach($statuses as $status)
                                <option value="{{ $status }}">{{ $status }}</option>
                                @endforeach
                            </select>
                            <input type="text" id="search-input" class="form-control form-control-sm mr-2" placeholder="Search...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered w-100" id="transactions-table" style="font-size: 11px;">
                                <thead class="bg-light">
                                    <tr>
                                        <th width="30px">
                                            <div class="form-check m-0 p-0">
                                                <input class="form-input p-0" type="checkbox" id="select-all-transactions">
                                            </div>
                                        </th>
                                        <th>No</th>
                                        <th class="sortable" >Site </th>
                                        <th class="sortable" >Unit </th>
                                        <th class="sortable" data-sort="status">Status <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="wo_number">Nomor WO <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="po_number">Nomor PO <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="do_number">{{ session('site_id') === 'IMK' ? 'No SPB' : 'Nomor DO' }} <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="created_at">Tanggal <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody id="transactions-table-body">
                                    <!-- Data will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span id="entries-info">Showing 0 to 0 of 0 entries</span>
                            </div>
                            <div id="pagination-container">
                                <!-- Pagination will be loaded dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invoices Section -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="bgwhite shadow-kit rounded-lg">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 mr-3 font-bold text-uppercase text-white">Daftar Invoice</h5>
                </div>
                <div class="d-flex">
                    <select id="invoice-status-filter" class="form-control form-control-sm mr-2">
                        <option value="">Semua Status</option>
                        <option value="Lunas">Lunas</option>
                        <option value="Belum Lunas">Belum Lunas</option>
                        <option value="Jatuh Tempo">Jatuh Tempo</option>
                    </select>
                    <input type="text" id="invoice-search-input" class="form-control form-control-sm" placeholder="Search...">
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered w-100" id="invoiced-transactions-table" style="font-size: 11px;">
                        <thead class="bg-light">
                            <tr>
                                <th>No</th>
                                <th class="sortable" data-sort="no_invoice">Nomor Invoice <i class="sort-icon mdi mdi-sort"></i></th>
                                <th class="sortable" data-sort="unit_list">Daftar Unit <i class="sort-icon mdi mdi-sort"></i></th>
                                <th class="sortable">Tanggal</th>
                                <th class="sortable">Customer</th>
                                <th class="sortable">Serial Number</th>
                                <th class="sortable">Jumlah</th>
                                <th class="sortable">Pajak</th>
                                <th class="sortable">Total</th>
                                <th class="sortable">Status</th>
                                <th class="sortable">Status Pembayaran</th>
                                <th>Dokumen</th>
                                <th>Download Invoice</th>
                                <th>Download Lampiran</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody id="invoiced-transactions-table-body">
                            <!-- Data will be loaded dynamically -->
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span id="invoiced-entries-info">Showing 0 to 0 of 0 entries</span>
                    </div>
                    <div id="invoiced-pagination-container">
                        <!-- Pagination will be loaded dynamically -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Status Modal -->
<div class="modal fade" id="payment-status-modal" tabindex="-1" role="dialog" aria-labelledby="payment-status-modal-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="payment-status-modal-label">Update Status Pembayaran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="payment-status-form" enctype="multipart/form-data">
                    <input type="hidden" id="invoice-id" name="invoice_id">
                    <div class="form-group mb-3">
                        <label for="payment-status" class="form-label">Status Pembayaran</label>
                        <select id="payment-status" name="payment_status" class="form-select">
                            <option value="Belum Lunas">Belum Lunas</option>
                            <option value="Lunas">Lunas</option>
                        </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="payment-date" class="form-label">Tanggal Pembayaran</label>
                        <input type="date" id="payment-date" name="payment_date" class="form-control">
                    </div>
                    <div class="form-group mb-3">
                        <label for="payment-notes" class="form-label">Catatan</label>
                        <textarea id="payment-notes" name="payment_notes" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="form-group mb-3" id="document-upload-container">
                        <label for="document" class="form-label">Dokumen Invoice Asli</label>
                        <input type="file" id="document" name="document" class="form-control" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                        <small class="text-muted">Unggah dokumen invoice asli (PDF, JPG, JPEG, PNG, DOC, DOCX). Maksimal 10MB.</small>
                        <div id="document-preview" class="mt-2 d-none">
                            <p>Dokumen saat ini: <span id="current-document-name"></span>
                                <a href="#" id="view-current-document" target="_blank" class="btn btn-sm btn-info ms-2">
                                    <i class="mdi mdi-file"></i> Lihat
                                </a>
                            </p>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="font-size: 11px;">Tutup</button>
                <button type="button" class="btn btn-primary" id="save-payment-status-btn" style="font-size: 11px;">Simpan</button>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Review Modal -->
<div class="modal fade" id="transaction-details-modal" tabindex="-1" role="dialog" aria-labelledby="transaction-details-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white p-2">
                <div class="d-flex align-items-center">
                    <button type="button" id="btn-toggle-status-form" class="btn btn-light btn-sm me-2">
                        <i class="mdi mdi-pencil"></i> Ubah Status
                    </button>
                    <button type="button" id="btn-toggle-notes-form" class="btn btn-info btn-sm me-2">
                        <i class="mdi mdi-note-edit"></i> Catatan Sales
                    </button>
                    <button type="button" id="btn-close-main-modal" class="btn btn-danger btn-sm me-2">
                        <i class="mdi mdi-close"></i> Tutup
                    </button>
                    <h5 class="modal-title text-white font-bold ms-2" id="transaction-details-modal-label">Review Transaksi Unit</h5>
                </div>
                <div>
                    <button type="button" id="toggle-details-btn" class="btn btn-light btn-sm">
                        <i class="mdi mdi-eye-off"></i> <span id="toggle-details-text">Sembunyikan Detail</span>
                    </button>
                </div>
            </div>
            <div class="modal-body p-0">
                <div class="d-flex h-100" style="font-size: 11px;">
                    <!-- Left Column: Transaction Details -->
                    <div id="transaction-details-panel" class="transaction-details" style="width: 30%; overflow-y: auto; transition: all 0.3s ease;">
                        <div class="card h-100 rounded-0 border-0">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 font-bold">Detail Transaksi</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <table class="table table-sm table-bordered">
                                            <tr>
                                                <th class="bg-light">Unit Name</th>
                                                <td id="detail-unit-type"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Unit Code</th>
                                                <td id="detail-unit-code"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Site</th>
                                                <td id="detail-site"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">No. Invoice</th>
                                                <td id="detail-invoice-number"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">No. PO</th>
                                                <td id="detail-po-number"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light" id="detail-do-number-label">{{ session('site_id') === 'IMK' ? 'No SPB' : 'Nomor DO' }}</th>
                                                <td id="detail-do-number"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Tanggal Dibuat</th>
                                                <td id="detail-created-at"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Catatan Sales</th>
                                                <td id="detail-sales-notes"></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="row" style="font-size: 11px;">
                                    <div class="col-md-12">
                                        <h5 class="font-bold">Daftar Parts</h5>
                                        <div class="table-responsive">
                                            <table class="table table-sm table-bordered">
                                                <thead class="bg-light font-bold">
                                                    <tr>
                                                        <th>Part Code</th>
                                                        <th>Part Name</th>
                                                        <th>Quantity</th>
                                                        <th>Price</th>
                                                        <th>Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="detail-parts-table-body">
                                                    <!-- Parts will be loaded dynamically -->
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <th colspan="4" class="text-end">Total</th>
                                                        <th id="detail-total-price"></th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column: Attachment Preview -->
                    <div id="attachment-preview-panel" class="attachment-preview" style="width: 70%; transition: all 0.3s ease;">
                        <div id="attachment-container" class="h-100">
                            <!-- Attachment will be loaded here -->
                            <div id="attachment-preview" class="h-100">
                                <!-- Preview will be shown here with no margins or padding -->
                            </div>
                            <div id="attachment-actions" class="position-absolute" style="bottom: 20px; right: 20px; z-index: 1000;">
                                <a id="download-attachment" href="#" class="btn btn-primary" target="_blank">
                                    <i class="mdi mdi-download"></i> Download Lampiran
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Update Form (Hidden by default) -->
                <div id="status-update-container" class="position-absolute shadow-kit" style="display: none; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 1050;">
                    <div class="row w-fit-content">
                        <div class="shadow-kit p-4 bg-white rounded" style="min-width: 600px;">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center ">
                                <h5 class="mb-0 font-bold">Update Status</h5>
                                <button type="button" id="close-status-form" class="btn-close" aria-label="Close"></button>
                            </div>
                            <div class="card-body">
                                <form id="update-status-form">
                                    <input type="hidden" id="transaction-id">
                                    <div class="form-group mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select id="status" name="status" class="btn btn-sm btn-primary">
                                            <option value="Ready PO">Ready PO</option>
                                            <option value="Pending">Pending</option>
                                            <option value="Perbaikan">Perbaikan</option>
                                            <option value="Selesai">Selesai</option>
                                        </select>
                                    </div>
                                    <div class="form-group mb-3">
                                        <label for="sales_notes" class="form-label">Catatan Sales</label>
                                        <textarea id="sales_notes" name="sales_notes" class="form-control" rows="4" placeholder="Tambahkan catatan sales untuk transaksi ini..."></textarea>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="mdi mdi-content-save"></i> Simpan Perubahan
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sales Notes Update Form (Hidden by default) -->
                <div id="notes-update-container" class="position-absolute shadow-kit" style="display: none; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 1050;">
                    <div class="row w-fit-content">
                        <div class="shadow-kit p-4 bg-white rounded" style="min-width: 600px;">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center ">
                                <h5 class="mb-0 font-bold">Update Catatan Sales</h5>
                                <button type="button" id="close-notes-form" class="btn-close" aria-label="Close"></button>
                            </div>
                            <div class="card-body">
                                <form id="update-notes-form">
                                    <input type="hidden" id="notes-transaction-id">
                                    <div class="form-group mb-3">
                                        <label for="notes_sales_notes" class="form-label">Catatan Sales</label>
                                        <textarea id="notes_sales_notes" name="sales_notes" class="form-control" rows="6" placeholder="Tambahkan catatan sales untuk transaksi ini..."></textarea>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="mdi mdi-content-save"></i> Simpan Catatan
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add JavaScript for toggling details panel -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggleDetailsBtn = document.getElementById('toggle-details-btn');
    const toggleDetailsText = document.getElementById('toggle-details-text');
    const detailsPanel = document.getElementById('transaction-details-panel');
    const previewPanel = document.getElementById('attachment-preview-panel');

    toggleDetailsBtn.addEventListener('click', function() {
        if (detailsPanel.style.width === '0px' || detailsPanel.style.width === '0%') {
            // Show details
            detailsPanel.style.width = '30%';
            previewPanel.style.width = '70%';
            toggleDetailsText.textContent = 'Sembunyikan Detail';
            toggleDetailsBtn.innerHTML = '<i class="mdi mdi-eye-off"></i> <span id="toggle-details-text">Sembunyikan Detail</span>';
        } else {
            // Hide details
            detailsPanel.style.width = '0%';
            previewPanel.style.width = '100%';
            toggleDetailsText.textContent = 'Tampilkan Detail';
            toggleDetailsBtn.innerHTML = '<i class="mdi mdi-eye"></i> <span id="toggle-details-text">Tampilkan Detail</span>';
        }
    });
});
</script>

<style>
/* Ensure attachment preview takes full height and has no padding */
#attachment-preview iframe,
#attachment-preview embed,
#attachment-preview object,
#attachment-preview img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border: none;
}

/* Hide scrollbar when not needed */
.modal-body {
    overflow: hidden;
}

/* Ensure the modal takes full screen */
.modal-fullscreen {
    width: 100vw;
    max-width: none;
    height: 100vh;
    margin: 0;
}

.modal-fullscreen .modal-content {
    height: 100vh;
    border: 0;
    border-radius: 0;
}

/* Transition for smooth panel resizing */
.transaction-details, .attachment-preview {
    transition: width 0.3s ease;
}
</style>

<!-- Include Invoice Form Modal -->
@include('sales.invoice-form-modal')
@endsection